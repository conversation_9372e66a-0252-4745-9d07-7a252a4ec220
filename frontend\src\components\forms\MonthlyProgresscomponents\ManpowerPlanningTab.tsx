import React from "react";
import { Controller, useFormContext } from "react-hook-form";
import { MonthlyProgressSchemaType } from "../../../schemas/monthlyProgress/MonthlyProgressSchema";
import { Box, Button, Table, TableBody, TableCell, TableContainer, TableFooter, TableHead, TableRow, TextField } from "@mui/material";


const ManpowerPlanningTab: React.FC = () => {
  const { control, formState: { errors } } = useFormContext<MonthlyProgressSchemaType>();

  return (
    <Box>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Work Assignment</TableCell>
              <TableCell>Assignee</TableCell>
              <TableCell>Planned</TableCell>
              <TableCell>Consumed</TableCell>
              <TableCell>Balance</TableCell>
              <TableCell>Next Month Planning</TableCell>
              <TableCell>Comments</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              <TableCell>
                <Controller
                  name="workAssignment"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Work Assignment"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.workAssignment}
                      helperText={errors.workAssignment?.message || ''}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                <Controller
                  name="assignee"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Assignee"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.assignee}
                      helperText={errors.assignee?.message || ''}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                <Controller
                  name="planned"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Planned"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.planned}
                      helperText={errors.planned?.message || ''}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                <Controller
                  name="consumed"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Consumed"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.consumed}
                      helperText={errors.consumed?.message || ''}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                <Controller
                  name="balance"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Balance"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.balance}
                      helperText={errors.balance?.message || ''}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                <Controller
                  name="nextMonthPlanning"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Next Month Planning"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.nextMonthPlanning}
                      helperText={errors.nextMonthPlanning?.message || ''}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                <Controller
                  name="manpowerComments"
                  control={control}
                  render={({ field }) => (
                    <TextField
                      fullWidth
                      label="Comments"
                      value={field.value}
                      onChange={field.onChange}
                      error={!!errors.manpowerComments}
                      helperText={errors.manpowerComments?.message || ''}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ManpowerPlanningTab;
